const fs = require('fs');
const path = require('path');

/**
 * Custom semantic-release plugin to update VERSION and DATETIME in service worker
 */
module.exports = {
  prepare: async (pluginConfig, context) => {
    const { nextRelease, logger } = context;
    const swPath = path.join(process.cwd(), 'sw.js');
    
    try {
      // Read the current service worker file
      let swContent = fs.readFileSync(swPath, 'utf8');
      
      // Get current datetime in ISO format
      const currentDateTime = new Date().toISOString();
      
      // Update VERSION
      swContent = swContent.replace(
        /const VERSION = '[^']*';/,
        `const VERSION = '${nextRelease.version}';`
      );
      
      // Update DATETIME
      swContent = swContent.replace(
        /const DATETIME = '[^']*';/,
        `const DATETIME = '${currentDateTime}';`
      );
      
      // Write the updated content back to the file
      fs.writeFileSync(swPath, swContent, 'utf8');
      
      logger.log(`Updated sw.js with version ${nextRelease.version} and datetime ${currentDateTime}`);
    } catch (error) {
      logger.error('Failed to update sw.js:', error);
      throw error;
    }
  }
};
