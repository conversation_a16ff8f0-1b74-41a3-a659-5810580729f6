/**
 * Test script to verify the service worker update plugin works correctly
 */
const fs = require('fs');
const path = require('path');

// Import our custom plugin
const updateSwPlugin = require('./update-sw-version.js');

async function testPlugin() {
  console.log('Testing service worker update plugin...');
  
  // Create a mock context
  const mockContext = {
    nextRelease: {
      version: '1.0.0-test'
    },
    logger: {
      log: (msg) => console.log('[PLUGIN]', msg),
      error: (msg) => console.error('[PLUGIN ERROR]', msg)
    }
  };
  
  // Read current sw.js content
  const swPath = path.join(__dirname, 'sw.js');
  const originalContent = fs.readFileSync(swPath, 'utf8');
  console.log('Original VERSION line:', originalContent.match(/const VERSION = '[^']*';/)?.[0]);
  console.log('Original DATETIME line:', originalContent.match(/const DATETIME = '[^']*';/)?.[0]);
  
  try {
    // Run the plugin
    await updateSwPlugin.prepare({}, mockContext);
    
    // Read updated content
    const updatedContent = fs.readFileSync(swPath, 'utf8');
    console.log('Updated VERSION line:', updatedContent.match(/const VERSION = '[^']*';/)?.[0]);
    console.log('Updated DATETIME line:', updatedContent.match(/const DATETIME = '[^']*';/)?.[0]);
    
    // Restore original content
    fs.writeFileSync(swPath, originalContent, 'utf8');
    console.log('✅ Test completed successfully - original content restored');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    // Restore original content in case of error
    fs.writeFileSync(swPath, originalContent, 'utf8');
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  testPlugin();
}

module.exports = testPlugin;
